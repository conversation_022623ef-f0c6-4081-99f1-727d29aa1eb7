/**
 * @file IMU_Driver.h
 * @brief IMU传感器驱动头文件 (Arduino版本)
 * <AUTHOR> IMU Fusion Project
 */

#ifndef IMU_DRIVER_H
#define IMU_DRIVER_H

#include <Arduino.h>
#include <Wire.h>

// MPU6050寄存器地址
#define MPU6050_ADDR            0x68    // MPU6050 I2C地址
#define MPU6050_WHO_AM_I        0x75    // 设备ID寄存器
#define MPU6050_PWR_MGMT_1      0x6B    // 电源管理寄存器1
#define MPU6050_GYRO_CONFIG     0x1B    // 陀螺仪配置寄存器
#define MPU6050_ACCEL_CONFIG    0x1C    // 加速度计配置寄存器
#define MPU6050_ACCEL_XOUT_H    0x3B    // 加速度计X轴高字节
#define MPU6050_GYRO_XOUT_H     0x43    // 陀螺仪X轴高字节

// 量程配置
#define GYRO_RANGE_250DPS       0x00    // ±250°/s
#define GYRO_RANGE_500DPS       0x08    // ±500°/s
#define GYRO_RANGE_1000DPS      0x10    // ±1000°/s
#define GYRO_RANGE_2000DPS      0x18    // ±2000°/s

#define ACCEL_RANGE_2G          0x00    // ±2g
#define ACCEL_RANGE_4G          0x08    // ±4g
#define ACCEL_RANGE_8G          0x10    // ±8g
#define ACCEL_RANGE_16G         0x18    // ±16g

/**
 * @brief IMU驱动类
 */
class IMU_Driver {
private:
    uint8_t deviceAddress;
    float gyroScale;    // 陀螺仪量程转换因子
    float accelScale;   // 加速度计量程转换因子
    
    // 私有方法
    bool writeRegister(uint8_t reg, uint8_t value);
    uint8_t readRegister(uint8_t reg);
    bool readRegisters(uint8_t reg, uint8_t* buffer, uint8_t length);
    int16_t combineBytes(uint8_t high, uint8_t low);

public:
    /**
     * @brief 构造函数
     * @param addr I2C设备地址 (默认0x68)
     */
    IMU_Driver(uint8_t addr = MPU6050_ADDR);
    
    /**
     * @brief 初始化IMU传感器
     * @return true 成功，false 失败
     */
    bool begin();
    
    /**
     * @brief 读取IMU数据
     * @param accel_x 加速度计X轴输出 (g)
     * @param accel_y 加速度计Y轴输出 (g)
     * @param accel_z 加速度计Z轴输出 (g)
     * @param gyro_x 陀螺仪X轴输出 (度/秒)
     * @param gyro_y 陀螺仪Y轴输出 (度/秒)
     * @param gyro_z 陀螺仪Z轴输出 (度/秒)
     * @return true 成功，false 失败
     */
    bool readData(float& accel_x, float& accel_y, float& accel_z,
                  float& gyro_x, float& gyro_y, float& gyro_z);
    
    /**
     * @brief 读取原始数据
     * @param accel_raw 加速度计原始数据数组 [x, y, z]
     * @param gyro_raw 陀螺仪原始数据数组 [x, y, z]
     * @return true 成功，false 失败
     */
    bool readRawData(int16_t accel_raw[3], int16_t gyro_raw[3]);
    
    /**
     * @brief 检查设备连接
     * @return true 连接正常，false 连接异常
     */
    bool testConnection();
    
    /**
     * @brief 设置陀螺仪量程
     * @param range 量程设置 (GYRO_RANGE_250DPS, GYRO_RANGE_500DPS, 等)
     * @return true 成功，false 失败
     */
    bool setGyroRange(uint8_t range);
    
    /**
     * @brief 设置加速度计量程
     * @param range 量程设置 (ACCEL_RANGE_2G, ACCEL_RANGE_4G, 等)
     * @return true 成功，false 失败
     */
    bool setAccelRange(uint8_t range);
    
    /**
     * @brief 获取设备ID
     * @return 设备ID (正常应为0x68)
     */
    uint8_t getDeviceID();
};

#endif // IMU_DRIVER_H
