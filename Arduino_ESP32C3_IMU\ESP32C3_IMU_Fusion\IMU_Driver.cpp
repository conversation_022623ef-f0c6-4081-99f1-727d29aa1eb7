/**
 * @file IMU_Driver.cpp
 * @brief IMU传感器驱动实现 (Arduino版本)
 * <AUTHOR> IMU Fusion Project
 */

#include "IMU_Driver.h"

/**
 * @brief 构造函数
 */
IMU_Driver::IMU_Driver(uint8_t addr) {
    deviceAddress = addr;
    gyroScale = 131.0f;    // ±250°/s时的LSB/(°/s)
    accelScale = 16384.0f; // ±2g时的LSB/g
}

/**
 * @brief 初始化IMU传感器
 */
bool IMU_Driver::begin() {
    // 检查设备连接
    if (!testConnection()) {
        Serial.println("错误: 无法检测到MPU6050设备");
        return false;
    }
    
    Serial.print("检测到MPU6050设备，ID: 0x");
    Serial.println(getDeviceID(), HEX);
    
    // 复位设备
    if (!writeRegister(MPU6050_PWR_MGMT_1, 0x80)) {
        Serial.println("错误: 设备复位失败");
        return false;
    }
    delay(100); // 等待复位完成
    
    // 唤醒设备 (退出睡眠模式)
    if (!writeRegister(MPU6050_PWR_MGMT_1, 0x00)) {
        Serial.println("错误: 唤醒设备失败");
        return false;
    }
    delay(10);
    
    // 设置陀螺仪量程为±250°/s
    if (!setGyroRange(GYRO_RANGE_250DPS)) {
        Serial.println("错误: 设置陀螺仪量程失败");
        return false;
    }
    
    // 设置加速度计量程为±2g
    if (!setAccelRange(ACCEL_RANGE_2G)) {
        Serial.println("错误: 设置加速度计量程失败");
        return false;
    }
    
    Serial.println("MPU6050初始化完成");
    return true;
}

/**
 * @brief 读取IMU数据
 */
bool IMU_Driver::readData(float& accel_x, float& accel_y, float& accel_z,
                         float& gyro_x, float& gyro_y, float& gyro_z) {
    int16_t accel_raw[3];
    int16_t gyro_raw[3];
    
    if (!readRawData(accel_raw, gyro_raw)) {
        return false;
    }
    
    // 转换为物理单位
    accel_x = accel_raw[0] / accelScale;
    accel_y = accel_raw[1] / accelScale;
    accel_z = accel_raw[2] / accelScale;
    
    gyro_x = gyro_raw[0] / gyroScale;
    gyro_y = gyro_raw[1] / gyroScale;
    gyro_z = gyro_raw[2] / gyroScale;
    
    return true;
}

/**
 * @brief 读取原始数据
 */
bool IMU_Driver::readRawData(int16_t accel_raw[3], int16_t gyro_raw[3]) {
    uint8_t buffer[14];
    
    // 从0x3B开始连续读取14个字节
    // 0x3B-0x40: 加速度计数据 (X, Y, Z)
    // 0x41-0x42: 温度数据 (跳过)
    // 0x43-0x48: 陀螺仪数据 (X, Y, Z)
    if (!readRegisters(MPU6050_ACCEL_XOUT_H, buffer, 14)) {
        return false;
    }
    
    // 组合高低字节
    accel_raw[0] = combineBytes(buffer[0], buffer[1]);   // ACCEL_X
    accel_raw[1] = combineBytes(buffer[2], buffer[3]);   // ACCEL_Y
    accel_raw[2] = combineBytes(buffer[4], buffer[5]);   // ACCEL_Z
    
    // 跳过温度数据 (buffer[6], buffer[7])
    
    gyro_raw[0] = combineBytes(buffer[8], buffer[9]);    // GYRO_X
    gyro_raw[1] = combineBytes(buffer[10], buffer[11]);  // GYRO_Y
    gyro_raw[2] = combineBytes(buffer[12], buffer[13]);  // GYRO_Z
    
    return true;
}

/**
 * @brief 检查设备连接
 */
bool IMU_Driver::testConnection() {
    uint8_t deviceID = getDeviceID();
    return (deviceID == 0x68);
}

/**
 * @brief 设置陀螺仪量程
 */
bool IMU_Driver::setGyroRange(uint8_t range) {
    if (!writeRegister(MPU6050_GYRO_CONFIG, range)) {
        return false;
    }
    
    // 更新量程转换因子
    switch (range) {
        case GYRO_RANGE_250DPS:
            gyroScale = 131.0f;
            break;
        case GYRO_RANGE_500DPS:
            gyroScale = 65.5f;
            break;
        case GYRO_RANGE_1000DPS:
            gyroScale = 32.8f;
            break;
        case GYRO_RANGE_2000DPS:
            gyroScale = 16.4f;
            break;
        default:
            return false;
    }
    
    return true;
}

/**
 * @brief 设置加速度计量程
 */
bool IMU_Driver::setAccelRange(uint8_t range) {
    if (!writeRegister(MPU6050_ACCEL_CONFIG, range)) {
        return false;
    }
    
    // 更新量程转换因子
    switch (range) {
        case ACCEL_RANGE_2G:
            accelScale = 16384.0f;
            break;
        case ACCEL_RANGE_4G:
            accelScale = 8192.0f;
            break;
        case ACCEL_RANGE_8G:
            accelScale = 4096.0f;
            break;
        case ACCEL_RANGE_16G:
            accelScale = 2048.0f;
            break;
        default:
            return false;
    }
    
    return true;
}

/**
 * @brief 获取设备ID
 */
uint8_t IMU_Driver::getDeviceID() {
    return readRegister(MPU6050_WHO_AM_I);
}

/**
 * @brief 写寄存器
 */
bool IMU_Driver::writeRegister(uint8_t reg, uint8_t value) {
    Wire.beginTransmission(deviceAddress);
    Wire.write(reg);
    Wire.write(value);
    return (Wire.endTransmission() == 0);
}

/**
 * @brief 读单个寄存器
 */
uint8_t IMU_Driver::readRegister(uint8_t reg) {
    Wire.beginTransmission(deviceAddress);
    Wire.write(reg);
    if (Wire.endTransmission() != 0) {
        return 0;
    }
    
    Wire.requestFrom(deviceAddress, (uint8_t)1);
    if (Wire.available()) {
        return Wire.read();
    }
    return 0;
}

/**
 * @brief 读多个寄存器
 */
bool IMU_Driver::readRegisters(uint8_t reg, uint8_t* buffer, uint8_t length) {
    Wire.beginTransmission(deviceAddress);
    Wire.write(reg);
    if (Wire.endTransmission() != 0) {
        return false;
    }
    
    Wire.requestFrom(deviceAddress, length);
    uint8_t i = 0;
    while (Wire.available() && i < length) {
        buffer[i++] = Wire.read();
    }
    
    return (i == length);
}

/**
 * @brief 组合高低字节
 */
int16_t IMU_Driver::combineBytes(uint8_t high, uint8_t low) {
    return (int16_t)((high << 8) | low);
}
