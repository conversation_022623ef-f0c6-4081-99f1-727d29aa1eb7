{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.28"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "SH5001_ESP32C3_Example", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "SH5001_ESP32C3_Example::@6890427a1f51a3e7e1df", "jsonFile": "target-SH5001_ESP32C3_Example-Debug-3651151826ad6f55a00e.json", "name": "SH5001_ESP32C3_Example", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/SH5001_ESP32C3_Example/cmake-build-debug", "source": "C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/SH5001_ESP32C3_Example"}, "version": {"major": 2, "minor": 6}}