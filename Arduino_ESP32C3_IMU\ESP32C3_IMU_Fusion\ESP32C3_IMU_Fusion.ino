/**
 * @file ESP32C3_IMU_Fusion.ino
 * @brief ESP32-C3 IMU Fusion Arduino项目
 * <AUTHOR> IMU Fusion Project
 * @version 1.0
 * @date 2025-01-02
 * 
 * 这是一个基于ESP32-C3和MPU6050的IMU数据融合项目
 * 使用Fusion库进行姿态估计和传感器数据融合
 */

#include <Wire.h>
#include "Fusion.h"
#include "IMU_Driver.h"

// 日志标签
const char* TAG = "IMU_FUSION";

// IMU数据采样周期 (100Hz = 10ms)
#define SAMPLE_PERIOD_MS    10
#define SAMPLE_PERIOD_S     (SAMPLE_PERIOD_MS / 1000.0f)

// I2C引脚定义 (ESP32-C3)
#define SDA_PIN 8
#define SCL_PIN 9

// 全局变量
FusionAhrs ahrs;
IMU_Driver imu;
unsigned long lastSampleTime = 0;

/**
 * @brief Arduino初始化函数
 */
void setup() {
  // 初始化串口
  Serial.begin(115200);
  while (!Serial) {
    delay(10);
  }
  
  Serial.println("========================================");
  Serial.println("ESP32-C3 IMU Fusion Arduino项目启动");
  Serial.println("========================================");
  
  // 初始化I2C
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(400000); // 400kHz
  
  Serial.println("I2C总线初始化完成");
  
  // 初始化IMU传感器
  if (!imu.begin()) {
    Serial.println("错误: IMU初始化失败!");
    Serial.println("请检查:");
    Serial.println("1. MPU6050连接是否正确");
    Serial.println("2. I2C引脚配置 (SDA=GPIO4, SCL=GPIO5)");
    Serial.println("3. 传感器供电是否正常");
    while (1) {
      delay(1000);
    }
  }
  
  Serial.println("IMU传感器初始化成功");
  
  // 初始化Fusion AHRS算法
  initializeFusion();
  
  Serial.println("Fusion AHRS算法初始化完成");
  Serial.println("系统准备就绪，开始数据采集...");
  Serial.println("========================================");
  
  lastSampleTime = millis();
}

/**
 * @brief Arduino主循环函数
 */
void loop() {
  unsigned long currentTime = millis();
  
  // 控制采样频率 (100Hz)
  if (currentTime - lastSampleTime >= SAMPLE_PERIOD_MS) {
    lastSampleTime = currentTime;
    
    // 读取IMU数据
    float accel_x, accel_y, accel_z;
    float gyro_x, gyro_y, gyro_z;
    
    if (imu.readData(accel_x, accel_y, accel_z, gyro_x, gyro_y, gyro_z)) {
      // 处理传感器数据
      processSensorData(accel_x, accel_y, accel_z, gyro_x, gyro_y, gyro_z);
      
      // 每秒输出一次结果 (降低串口输出频率)
      static unsigned long lastPrintTime = 0;
      if (currentTime - lastPrintTime >= 1000) {
        lastPrintTime = currentTime;
        printResults();
      }
    } else {
      Serial.println("警告: 读取IMU数据失败");
    }
  }
  
  // 短暂延时，避免占用过多CPU
  delay(1);
}

/**
 * @brief 初始化Fusion AHRS算法
 */
void initializeFusion() {
  // 初始化AHRS算法
  FusionAhrsInitialise(&ahrs);
  
  // 设置AHRS算法参数
  const FusionAhrsSettings settings = {
    .convention = FusionConventionNwu,          // 北-西-上坐标系
    .gain = 0.5f,                               // 融合增益
    .gyroscopeRange = 2000.0f,                  // 陀螺仪量程 (度/秒)
    .accelerationRejection = 10.0f,             // 加速度拒绝阈值 (度)
    .magneticRejection = 10.0f,                 // 磁场拒绝阈值 (度)
    .recoveryTriggerPeriod = 5 * (1000 / SAMPLE_PERIOD_MS), // 恢复触发周期 (5秒)
  };
  
  FusionAhrsSetSettings(&ahrs, &settings);
}

/**
 * @brief 处理传感器数据
 */
void processSensorData(float accel_x, float accel_y, float accel_z,
                      float gyro_x, float gyro_y, float gyro_z) {
  // 准备Fusion库所需的数据格式
  const FusionVector gyroscope = {
    .axis.x = gyro_x,
    .axis.y = gyro_y,
    .axis.z = gyro_z
  };
  
  const FusionVector accelerometer = {
    .axis.x = accel_x,
    .axis.y = accel_y,
    .axis.z = accel_z
  };
  
  // 更新AHRS算法 (仅使用陀螺仪和加速度计)
  FusionAhrsUpdateNoMagnetometer(&ahrs, gyroscope, accelerometer, SAMPLE_PERIOD_S);
}

/**
 * @brief 打印结果
 */
void printResults() {
  // 获取姿态角度
  const FusionEuler euler = FusionQuaternionToEuler(FusionAhrsGetQuaternion(&ahrs));
  
  // 获取线性加速度（去除重力）
  const FusionVector linearAcceleration = FusionAhrsGetLinearAcceleration(&ahrs);
  
  // 打印姿态角
  Serial.print("姿态角: Roll=");
  Serial.print(euler.angle.roll, 1);
  Serial.print("°, Pitch=");
  Serial.print(euler.angle.pitch, 1);
  Serial.print("°, Yaw=");
  Serial.print(euler.angle.yaw, 1);
  Serial.println("°");
  
  // 打印线性加速度
  Serial.print("线性加速度: X=");
  Serial.print(linearAcceleration.axis.x, 2);
  Serial.print("g, Y=");
  Serial.print(linearAcceleration.axis.y, 2);
  Serial.print("g, Z=");
  Serial.print(linearAcceleration.axis.z, 2);
  Serial.println("g");
  
  Serial.println("----------------------------------------");
}
