# 📦 ESP32-C3 IMU Fusion Arduino项目包

## 🎯 项目概述

这是一个完整的Arduino项目包，包含了在ESP32-C3上运行IMU数据融合算法所需的所有文件。

### 📋 包含内容

```
Arduino_ESP32C3_IMU/                    # 项目根目录
├── ESP32C3_IMU_Fusion/                 # Arduino项目文件夹
│   ├── ESP32C3_IMU_Fusion.ino         # 主程序文件
│   ├── IMU_Driver.h                    # IMU驱动头文件
│   └── IMU_Driver.cpp                  # IMU驱动实现文件
├── libraries/                          # Arduino库文件夹
│   └── Fusion/                         # Fusion传感器融合库
│       ├── library.properties          # 库属性配置
│       └── src/                        # 库源代码
│           ├── Fusion.h                # 主头文件
│           ├── FusionAhrs.h            # AHRS算法头文件
│           ├── FusionAhrs.c            # AHRS算法实现
│           ├── FusionAxes.h            # 坐标轴定义
│           ├── FusionCalibration.h     # 校准算法
│           ├── FusionCompass.h         # 罗盘算法头文件
│           ├── FusionCompass.c         # 罗盘算法实现
│           ├── FusionConvention.h      # 坐标系约定
│           ├── FusionMath.h            # 数学函数库
│           ├── FusionOffset.h          # 偏移校正头文件
│           └── FusionOffset.c          # 偏移校正实现
├── README.md                           # 项目说明文档
├── ARDUINO_SETUP_GUIDE.md             # Arduino IDE设置指南
└── PACKAGE_INFO.md                     # 本文件
```

## 🚀 快速开始

### 1. 系统要求
- **Arduino IDE**: 2.0+ (推荐) 或 1.8.19+
- **ESP32开发板包**: v2.0.0+
- **硬件**: ESP32-C3开发板 + MPU6050 IMU传感器

### 2. 安装步骤

#### 步骤1: 安装Arduino IDE和ESP32支持
请参考 `ARDUINO_SETUP_GUIDE.md` 获取详细的安装指南。

#### 步骤2: 安装项目文件

**方法A: 手动安装**
1. 将 `libraries/Fusion` 文件夹复制到Arduino库目录：
   - Windows: `C:\Users\<USER>\Documents\Arduino\libraries\`
   - macOS: `~/Documents/Arduino/libraries/`
   - Linux: `~/Arduino/libraries/`

2. 在Arduino IDE中打开 `ESP32C3_IMU_Fusion/ESP32C3_IMU_Fusion.ino`

**方法B: 直接使用**
1. 在Arduino IDE中选择 **文件** → **打开**
2. 选择 `ESP32C3_IMU_Fusion/ESP32C3_IMU_Fusion.ino`
3. Arduino IDE会自动识别项目结构

#### 步骤3: 硬件连接
```
ESP32-C3    MPU6050
--------    -------
3.3V    ->  VCC
GND     ->  GND
GPIO4   ->  SDA
GPIO5   ->  SCL
```

#### 步骤4: 配置和上传
1. 选择开发板: **工具** → **开发板** → **ESP32C3 Dev Module**
2. 选择串口: **工具** → **端口** → 选择对应串口
3. 点击上传按钮 (→)

## 🔧 核心功能

### IMU数据读取
- 支持MPU6050 6轴IMU传感器
- 实时读取陀螺仪和加速度计数据
- 自动量程配置和数据转换

### 传感器融合
- 使用Madgwick AHRS算法
- 高精度姿态估计 (Roll, Pitch, Yaw)
- 线性加速度计算 (去除重力影响)
- 加速度拒绝和角速度恢复

### Arduino兼容性
- 完全兼容Arduino IDE和框架
- 使用标准Wire库进行I2C通信
- 面向对象的驱动设计
- 详细的错误处理和日志输出

## 📊 技术规格

### 性能参数
- **采样频率**: 100Hz (可配置)
- **姿态精度**: ±1° (静态条件下)
- **响应时间**: <100ms
- **内存占用**: <50KB Flash, <10KB RAM

### 支持的传感器
- **当前支持**: MPU6050 (6轴: 陀螺仪 + 加速度计)
- **计划支持**: MPU9250 (9轴: 陀螺仪 + 加速度计 + 磁力计)

### 输出数据
- **姿态角**: Roll, Pitch, Yaw (度)
- **线性加速度**: X, Y, Z (g)
- **原始数据**: 陀螺仪和加速度计原始值

## 🛠️ 自定义配置

### 修改采样频率
在 `ESP32C3_IMU_Fusion.ino` 中：
```cpp
#define SAMPLE_PERIOD_MS    10    // 10ms = 100Hz
```

### 修改I2C引脚
在 `ESP32C3_IMU_Fusion.ino` 中：
```cpp
#define SDA_PIN 4    // I2C数据引脚
#define SCL_PIN 5    // I2C时钟引脚
```

### 调整AHRS参数
在 `initializeFusion()` 函数中：
```cpp
const FusionAhrsSettings settings = {
    .convention = FusionConventionNwu,
    .gain = 0.5f,                    // 融合增益 (0.1-2.0)
    .gyroscopeRange = 2000.0f,       // 陀螺仪量程
    .accelerationRejection = 10.0f,  // 加速度拒绝阈值
    .magneticRejection = 10.0f,      // 磁场拒绝阈值
    .recoveryTriggerPeriod = 500,    // 恢复触发周期
};
```

## 📈 预期输出

成功运行后，串口监视器将显示：

```
========================================
ESP32-C3 IMU Fusion Arduino项目启动
========================================
I2C总线初始化完成
检测到MPU6050设备，ID: 0x68
MPU6050初始化完成
Fusion AHRS算法初始化完成
系统准备就绪，开始数据采集...
========================================
姿态角: Roll=1.2°, Pitch=-0.8°, Yaw=45.6°
线性加速度: X=0.02g, Y=-0.01g, Z=0.00g
----------------------------------------
```

## 🔍 故障排除

### 常见问题
1. **编译错误**: 确保Fusion库正确安装
2. **IMU初始化失败**: 检查硬件连接
3. **数据异常**: 确保传感器静止校准
4. **上传失败**: 检查串口和开发板选择

详细的故障排除指南请参考 `README.md`。

## 📚 文档说明

- **README.md**: 完整的项目文档，包含详细的使用说明和API参考
- **ARDUINO_SETUP_GUIDE.md**: Arduino IDE和ESP32开发环境的详细设置指南
- **PACKAGE_INFO.md**: 本文件，项目包的概述和快速开始指南

## 🎯 应用场景

### 适用项目
- 🚁 无人机姿态控制
- 🤖 机器人平衡系统
- 📱 手势识别设备
- 🎮 体感游戏控制器
- 📐 数字水平仪
- 🏃 运动追踪设备

### 扩展可能
- WiFi数据传输
- 蓝牙连接
- SD卡数据记录
- OLED显示屏
- 多传感器融合
- 机器学习集成

## 📄 版本信息

- **项目版本**: v1.0.0
- **Fusion库版本**: v1.2.0
- **Arduino ESP32核心**: v2.0.0+
- **最后更新**: 2025-01-02

## 🤝 支持和贡献

如果您在使用过程中遇到问题或有改进建议，欢迎：
1. 查阅文档和故障排除指南
2. 在项目仓库提交Issue
3. 贡献代码改进

---

**祝您使用愉快！** 🎉
