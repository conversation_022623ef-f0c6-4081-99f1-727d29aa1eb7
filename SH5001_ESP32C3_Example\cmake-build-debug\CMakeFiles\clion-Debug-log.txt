"D:\CLion 2024.3\CLion 2024.1.6\bin\cmake\win\x64\bin\cmake.exe" -DCMAKE_BUILD_TYPE=Debug "-DCMAKE_MAKE_PROGRAM=D:/CLion 2024.3/CLion 2024.1.6/bin/ninja/win/x64/ninja.exe" -G Ninja -S C:\Users\<USER>\Desktop\IMU\SH5001_ESP32C3_Example\SH5001_ESP32C3_Example -B C:\Users\<USER>\Desktop\IMU\SH5001_ESP32C3_Example\SH5001_ESP32C3_Example\cmake-build-debug
-- The C compiler identification is GNU 13.1.0
-- The CXX compiler identification is GNU 13.1.0
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/gcc.exe - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/g++.exe - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Configuring done (24.1s)
-- Generating done (0.0s)
-- Build files have been written to: C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/SH5001_ESP32C3_Example/cmake-build-debug
