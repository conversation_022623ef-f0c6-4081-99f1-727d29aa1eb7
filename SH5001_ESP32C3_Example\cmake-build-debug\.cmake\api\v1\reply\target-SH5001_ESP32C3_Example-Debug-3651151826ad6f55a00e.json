{"artifacts": [{"path": "SH5001_ESP32C3_Example.exe"}, {"path": "SH5001_ESP32C3_Example.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 8, "parent": 0}, {"command": 1, "file": 0, "line": 6, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++14 -fdiagnostics-color=always"}], "includes": [{"backtrace": 2, "path": "C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/SH5001_ESP32C3_Example/."}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "14"}, "sourceIndexes": [0]}], "id": "SH5001_ESP32C3_Example::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "SH5001_ESP32C3_Example", "nameOnDisk": "SH5001_ESP32C3_Example.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}, {"name": "", "sourceIndexes": [2]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "SH5001.C", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "SH5001.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "SH5001_ESP32C3_Example.ino", "sourceGroupIndex": 2}], "type": "EXECUTABLE"}